<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">payment.provider.form.odoxpay</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='provider_credentials']" position="after">
                <group attrs="{'invisible': [('code', '!=', 'odoxpay')]}" name="odoxpay_credentials">
                    <field name="odoxpay_api_key" password="True" placeholder="pk_test_..." attrs="{'required': [('code', '=', 'odoxpay'), ('state', '!=', 'disabled')]}"/>
                    <field name="odoxpay_secret_key" password="True" placeholder="sk_test_..." attrs="{'required': [('code', '=', 'odoxpay'), ('state', '!=', 'disabled')]}"/>
                    <field name="odoxpay_webhook_secret" password="True" placeholder="whsec_..." help="Optional: Used to verify webhook signatures"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="payment_provider_onboarding_form" model="ir.ui.view">
        <field name="name">payment.provider.onboarding.form.odoxpay</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_onboarding_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='provider_credentials']" position="after">
                <group attrs="{'invisible': [('code', '!=', 'odoxpay')]}" name="odoxpay_credentials">
                    <field name="odoxpay_api_key" password="True" placeholder="pk_test_..." attrs="{'required': [('code', '=', 'odoxpay'), ('state', '!=', 'disabled')]}"/>
                    <field name="odoxpay_secret_key" password="True" placeholder="sk_test_..." attrs="{'required': [('code', '=', 'odoxpay'), ('state', '!=', 'disabled')]}"/>
                    <field name="odoxpay_webhook_secret" password="True" placeholder="whsec_..." help="Optional: Used to verify webhook signatures"/>
                </group>
            </xpath>
        </field>
    </record>

</odoo>
