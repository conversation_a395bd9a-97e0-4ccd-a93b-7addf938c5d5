# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
import pprint

import requests
from werkzeug import urls

from odoo import _, fields, models
from odoo.exceptions import ValidationError

from odoo.addons.payment_odoxpay import const


_logger = logging.getLogger(__name__)


class PaymentProvider(models.Model):
    _inherit = 'payment.provider'

    code = fields.Selection(
        selection_add=[('odoxpay', "OdoxPay")], ondelete={'odoxpay': 'set default'}
    )
    odoxpay_api_key = fields.Char(
        string="OdoxPay API Key",
        required_if_provider='odoxpay',
        groups='base.group_system',
    )
    odoxpay_secret_key = fields.Char(
        string="OdoxPay Secret Key",
        required_if_provider='odoxpay',
        groups='base.group_system',
    )
    odoxpay_webhook_secret = fields.Char(
        string="OdoxPay Webhook Secret",
        groups='base.group_system',
        help="Secret used to verify webhook signatures from OdoxPay"
    )

    # === BUSINESS METHODS === #

    def _get_supported_currencies(self):
        """ Override of `payment` to return the supported currencies. """
        supported_currencies = super()._get_supported_currencies()
        if self.code == 'odoxpay':
            supported_currencies = supported_currencies.filtered(
                lambda c: c.name in const.SUPPORTED_CURRENCIES
            )
        return supported_currencies

    def _odoxpay_make_request(self, endpoint, payload=None, method='POST'):
        """ Make a request to OdoxPay API at the specified endpoint.

        Note: self.ensure_one()

        :param str endpoint: The endpoint to be reached by the request.
        :param dict payload: The payload of the request.
        :param str method: The HTTP method of the request.
        :return The JSON-formatted content of the response.
        :rtype: dict
        :raise ValidationError: If an HTTP error occurs.
        """
        self.ensure_one()

        base_url = 'https://api.odoxpay.com' if self.state == 'enabled' else 'https://sandbox-api.odoxpay.com'
        url = urls.url_join(base_url, endpoint)
        
        headers = {
            'Authorization': f'Bearer {self.odoxpay_api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'Odoo Payment Provider OdoxPay/1.0',
        }

        try:
            response = requests.request(method, url, json=payload, headers=headers, timeout=60)
            response.raise_for_status()
        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as error:
            _logger.exception("Unable to reach endpoint at %s.", url)
            raise ValidationError("OdoxPay: " + _("Could not establish the connection to the API.")) from error
        except requests.exceptions.HTTPError as error:
            _logger.exception("Invalid API request at %s with data:\n%s", url, pprint.pformat(payload))
            raise ValidationError("OdoxPay: " + _("The communication with the API failed.")) from error

        return response.json()

    def _get_default_payment_method_codes(self):
        """ Override of `payment` to return the default payment method codes. """
        default_codes = super()._get_default_payment_method_codes()
        if self.code != 'odoxpay':
            return default_codes
        return const.DEFAULT_PAYMENT_METHOD_CODES
