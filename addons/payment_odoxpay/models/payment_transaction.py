# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
import pprint

from werkzeug import urls

from odoo import _, models
from odoo.exceptions import ValidationError

from odoo.addons.payment import utils as payment_utils
from odoo.addons.payment_odoxpay import const


_logger = logging.getLogger(__name__)


class PaymentTransaction(models.Model):
    _inherit = 'payment.transaction'

    def _get_specific_rendering_values(self, processing_values):
        """ Override of `payment` to return OdoxPay-specific rendering values.

        Note: self.ensure_one() from `_get_processing_values`.

        :param dict processing_values: The generic and specific processing values of the transaction.
        :return: The dict of provider-specific processing values.
        :rtype: dict
        """
        res = super()._get_specific_rendering_values(processing_values)
        if self.provider_code != 'odoxpay':
            return res

        # Prepare the payment data for OdoxPay
        payment_data = {
            'amount': payment_utils.to_minor_currency_units(self.amount, self.currency_id),
            'currency': self.currency_id.name,
            'reference': self.reference,
            'description': f"Payment for {self.reference}",
            'return_url': urls.url_join(self.provider_id.get_base_url(), '/payment/odoxpay/return'),
            'cancel_url': urls.url_join(self.provider_id.get_base_url(), '/payment/odoxpay/cancel'),
            'webhook_url': urls.url_join(self.provider_id.get_base_url(), '/payment/odoxpay/webhook'),
            'customer': {
                'name': self.partner_name,
                'email': self.partner_email,
                'phone': self.partner_phone,
            },
            'metadata': {
                'odoo_transaction_id': self.id,
                'odoo_reference': self.reference,
            }
        }

        # Create payment session with OdoxPay
        try:
            response_data = self.provider_id._odoxpay_make_request('/payments', payment_data)
            payment_session_id = response_data.get('session_id')
            checkout_url = response_data.get('checkout_url')
            
            if not payment_session_id or not checkout_url:
                raise ValidationError(_("OdoxPay did not return a valid payment session."))
                
        except ValidationError:
            raise
        except Exception as error:
            _logger.exception("Error creating OdoxPay payment session: %s", error)
            raise ValidationError(_("Unable to create payment session with OdoxPay.")) from error

        # Store the session ID for later reference
        self.provider_reference = payment_session_id

        return {
            'checkout_url': checkout_url,
            'session_id': payment_session_id,
        }

    def _get_tx_from_notification_data(self, provider_code, notification_data):
        """ Override of `payment` to find the transaction based on OdoxPay data.

        :param str provider_code: The code of the provider that handled the transaction.
        :param dict notification_data: The notification data sent by the provider.
        :return: The transaction if found.
        :rtype: recordset of `payment.transaction`
        :raise ValidationError: If inconsistent data were received.
        :raise ValidationError: If the data match no transaction.
        """
        tx = super()._get_tx_from_notification_data(provider_code, notification_data)
        if provider_code != 'odoxpay' or len(tx) == 1:
            return tx

        # Find the transaction based on the reference or session ID
        reference = notification_data.get('reference')
        session_id = notification_data.get('session_id')
        
        if reference:
            tx = self.search([('reference', '=', reference), ('provider_code', '=', 'odoxpay')])
        elif session_id:
            tx = self.search([('provider_reference', '=', session_id), ('provider_code', '=', 'odoxpay')])
        
        if not tx:
            raise ValidationError("OdoxPay: " + _("No transaction found matching reference %s.", reference or session_id))
        elif len(tx) > 1:
            raise ValidationError("OdoxPay: " + _("Multiple transactions found matching reference %s.", reference or session_id))
        
        return tx

    def _process_notification_data(self, notification_data):
        """ Override of `payment` to process the transaction based on OdoxPay data.

        Note: self.ensure_one()

        :param dict notification_data: The notification data sent by the provider.
        :return: None
        :raise ValidationError: If inconsistent data were received.
        """
        super()._process_notification_data(notification_data)
        if self.provider_code != 'odoxpay':
            return

        # Update the provider reference if not already set
        session_id = notification_data.get('session_id')
        if session_id and not self.provider_reference:
            self.provider_reference = session_id

        # Extract the payment status
        payment_status = notification_data.get('status')
        if not payment_status:
            raise ValidationError("OdoxPay: " + _("Received data with missing payment status."))

        # Update the transaction state based on the payment status
        if payment_status in const.TRANSACTION_STATUS_MAPPING['pending']:
            self._set_pending()
        elif payment_status in const.TRANSACTION_STATUS_MAPPING['done']:
            self._set_done()
        elif payment_status in const.TRANSACTION_STATUS_MAPPING['canceled']:
            self._set_canceled()
        elif payment_status in const.TRANSACTION_STATUS_MAPPING['error']:
            error_code = notification_data.get('error_code', 'general_error')
            error_message = const.ERROR_MESSAGE_MAPPING.get(error_code, const.ERROR_MESSAGE_MAPPING['general_error'])
            self._set_error(error_message)
        else:
            _logger.warning("Received data with invalid payment status (%s) for transaction with reference %s", payment_status, self.reference)
            self._set_error("OdoxPay: " + _("Received data with invalid payment status: %s", payment_status))
