<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payment Provider: OdoxPay</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 200px;
            height: auto;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .feature h3 {
            margin-top: 0;
            color: #007bff;
        }
        .currencies {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        .currency {
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .setup-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Payment Provider: OdoxPay</h1>
        <p>Secure and reliable payment processing for your Odoo e-commerce</p>
    </div>

    <div class="features">
        <div class="feature">
            <h3>🌍 Multi-Currency Support</h3>
            <p>Accept payments in multiple currencies:</p>
            <div class="currencies">
                <span class="currency">USD</span>
                <span class="currency">EUR</span>
                <span class="currency">BRL</span>
                <span class="currency">ARS</span>
                <span class="currency">MXN</span>
                <span class="currency">COP</span>
                <span class="currency">CLP</span>
                <span class="currency">PEN</span>
            </div>
        </div>

        <div class="feature">
            <h3>💳 Payment Methods</h3>
            <p>Support for major credit and debit cards:</p>
            <ul>
                <li>Visa</li>
                <li>Mastercard</li>
                <li>American Express</li>
                <li>Discover</li>
            </ul>
        </div>

        <div class="feature">
            <h3>🔒 Security</h3>
            <p>Enterprise-grade security features:</p>
            <ul>
                <li>Webhook signature verification</li>
                <li>Secure API communication</li>
                <li>PCI DSS compliance</li>
                <li>Fraud protection</li>
            </ul>
        </div>

        <div class="feature">
            <h3>⚙️ Advanced Features</h3>
            <ul>
                <li>Manual capture support</li>
                <li>Full refund capability</li>
                <li>Sandbox testing environment</li>
                <li>Real-time webhook notifications</li>
                <li>Transaction status tracking</li>
            </ul>
        </div>
    </div>

    <div class="setup-steps">
        <h3>🚀 Quick Setup</h3>
        <div class="step">1. Install the OdoxPay payment provider module</div>
        <div class="step">2. Go to Accounting → Configuration → Payment Providers</div>
        <div class="step">3. Create or configure the OdoxPay provider</div>
        <div class="step">4. Enter your API credentials (API Key, Secret Key)</div>
        <div class="step">5. Configure webhook secret for enhanced security (optional)</div>
        <div class="step">6. Enable the provider and start accepting payments!</div>
    </div>

    <div class="feature">
        <h3>📚 Documentation</h3>
        <p>For detailed configuration and API documentation, please refer to:</p>
        <ul>
            <li>OdoxPay API Documentation</li>
            <li>Odoo Payment Provider Guide</li>
            <li>Module README file</li>
        </ul>
    </div>

    <div class="feature">
        <h3>🛠️ Support</h3>
        <p>Need help? Contact our support team or check the documentation for troubleshooting guides.</p>
    </div>
</body>
</html>
