# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo.tools import LazyTranslate
_lt = LazyTranslate(__name__)


# Currency codes of the currencies supported by OdoxPay in ISO 4217 format.
SUPPORTED_CURRENCIES = [
    'USD',  # US Dollars
    'EUR',  # Euro
    'BRL',  # Brazilian Real
    'ARS',  # Argentinian Peso
    'MXN',  # Mexican Peso
    'COP',  # Colombian Peso
    'CLP',  # Chilean Peso
    'PEN',  # Peruvian Sol
]

# The codes of the payment methods to activate when OdoxPay is activated.
DEFAULT_PAYMENT_METHOD_CODES = {
    # Primary payment methods.
    'card',
    # Brand payment methods.
    'visa',
    'mastercard',
    'amex',
    'discover',
}

# Mapping of payment method codes to OdoxPay codes.
PAYMENT_METHODS_MAPPING = {
    'card': 'credit_card,debit_card',
    'visa': 'visa',
    'mastercard': 'mastercard',
    'amex': 'amex',
    'discover': 'discover',
}

# Mapping of transaction states to OdoxPay payment statuses.
TRANSACTION_STATUS_MAPPING = {
    'pending': ('pending', 'processing', 'authorized'),
    'done': ('completed', 'approved', 'captured'),
    'canceled': ('cancelled', 'voided', 'failed'),
    'error': ('rejected', 'error', 'declined'),
}

# Mapping of error states to OdoxPay error messages.
ERROR_MESSAGE_MAPPING = {
    'insufficient_funds': _lt("Insufficient funds on the card."),
    'card_declined': _lt("The card was declined by the issuer."),
    'expired_card': _lt("The card has expired."),
    'invalid_card': _lt("Invalid card number."),
    'invalid_cvv': _lt("Invalid CVV code."),
    'processing_error': _lt("An error occurred while processing the payment."),
    'network_error': _lt("Network error. Please try again."),
    'authentication_failed': _lt("Authentication failed. Please check your credentials."),
    'invalid_amount': _lt("Invalid payment amount."),
    'duplicate_transaction': _lt("Duplicate transaction detected."),
    'general_error': _lt("An unexpected error occurred. Please try again."),
}
