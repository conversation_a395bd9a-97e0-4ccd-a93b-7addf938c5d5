# Part of Odoo. See LICENSE file for full copyright and licensing details.

import hashlib
import hmac
import json
import logging
import pprint

from werkzeug.exceptions import Forbidden

from odoo import http
from odoo.exceptions import ValidationError
from odoo.http import request


_logger = logging.getLogger(__name__)


class OdoxPayController(http.Controller):
    _return_url = '/payment/odoxpay/return'
    _cancel_url = '/payment/odoxpay/cancel'
    _webhook_url = '/payment/odoxpay/webhook'

    @http.route(_return_url, type='http', auth='public', methods=['GET', 'POST'], csrf=False)
    def odoxpay_return_from_checkout(self, **data):
        """ Process the notification data sent by OdoxPay after redirection from checkout.

        :param dict data: The notification data.
        """
        _logger.info("Handling redirection from OdoxPay with data:\n%s", pprint.pformat(data))
        
        # Extract transaction reference from the data
        reference = data.get('reference')
        if not reference:
            _logger.warning("OdoxPay return: missing reference in data")
            return request.redirect('/payment/status')

        # Find and process the transaction
        try:
            tx_sudo = request.env['payment.transaction'].sudo()._get_tx_from_notification_data('odoxpay', data)
            tx_sudo._process_notification_data(data)
        except ValidationError as error:
            _logger.exception("Unable to handle the notification data; skipping to status page")

        return request.redirect('/payment/status')

    @http.route(_cancel_url, type='http', auth='public', methods=['GET', 'POST'], csrf=False)
    def odoxpay_cancel_checkout(self, **data):
        """ Process the notification data sent by OdoxPay when the customer cancels the payment.

        :param dict data: The notification data.
        """
        _logger.info("Handling cancellation from OdoxPay with data:\n%s", pprint.pformat(data))
        
        # Extract transaction reference from the data
        reference = data.get('reference')
        if reference:
            try:
                tx_sudo = request.env['payment.transaction'].sudo()._get_tx_from_notification_data('odoxpay', data)
                # Set transaction as canceled
                cancel_data = dict(data, status='cancelled')
                tx_sudo._process_notification_data(cancel_data)
            except ValidationError as error:
                _logger.exception("Unable to handle the cancellation data")

        return request.redirect('/payment/status')

    @http.route(_webhook_url, type='http', auth='public', methods=['POST'], csrf=False)
    def odoxpay_webhook(self):
        """ Process the notification data sent by OdoxPay to the webhook.

        :return: An empty string to acknowledge the notification.
        :rtype: str
        """
        data = json.loads(request.httprequest.data)
        _logger.info("Notification received from OdoxPay with data:\n%s", pprint.pformat(data))

        try:
            # Verify the webhook signature if webhook secret is configured
            self._verify_webhook_signature(data)
            
            # Find and process the transaction
            tx_sudo = request.env['payment.transaction'].sudo()._get_tx_from_notification_data('odoxpay', data)
            tx_sudo._process_notification_data(data)
            
        except ValidationError as error:
            _logger.exception("Unable to handle the notification data")
            return 'error'

        return 'ok'

    def _verify_webhook_signature(self, data):
        """ Verify the signature of the webhook notification.

        :param dict data: The notification data.
        :return: None
        :raise Forbidden: If the signature is invalid.
        """
        # Get the signature from headers
        signature = request.httprequest.headers.get('X-OdoxPay-Signature')
        if not signature:
            _logger.warning("OdoxPay webhook: missing signature header")
            return  # Skip verification if no signature provided

        # Find the provider to get the webhook secret
        session_id = data.get('session_id')
        reference = data.get('reference')
        
        if session_id:
            tx_sudo = request.env['payment.transaction'].sudo().search([
                ('provider_reference', '=', session_id),
                ('provider_code', '=', 'odoxpay')
            ], limit=1)
        elif reference:
            tx_sudo = request.env['payment.transaction'].sudo().search([
                ('reference', '=', reference),
                ('provider_code', '=', 'odoxpay')
            ], limit=1)
        else:
            _logger.warning("OdoxPay webhook: no session_id or reference found in data")
            return

        if not tx_sudo or not tx_sudo.provider_id.odoxpay_webhook_secret:
            _logger.warning("OdoxPay webhook: no webhook secret configured")
            return

        # Verify the signature
        webhook_secret = tx_sudo.provider_id.odoxpay_webhook_secret
        payload = request.httprequest.data
        expected_signature = hmac.new(
            webhook_secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()

        if not hmac.compare_digest(signature, expected_signature):
            _logger.warning("OdoxPay webhook: invalid signature")
            raise Forbidden("Invalid webhook signature")
