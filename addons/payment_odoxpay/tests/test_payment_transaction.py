# Part of Odoo. See LICENSE file for full copyright and licensing details.

from unittest.mock import patch

from odoo.tests import tagged

from odoo.addons.payment.tests.common import PaymentCommon


@tagged('post_install', '-at_install')
class TestPaymentTransaction(PaymentCommon):

    def setUp(self):
        super().setUp()
        self.provider = self._prepare_provider('odoxpay', update_values={
            'odoxpay_api_key': 'pk_test_dummy_key',
            'odoxpay_secret_key': 'sk_test_dummy_secret',
            'odoxpay_webhook_secret': 'whsec_dummy_webhook_secret',
        })

    def test_get_tx_from_notification_data_with_reference(self):
        """ Test finding transaction by reference. """
        tx = self._create_transaction('redirect', reference='test-ref-123')
        
        notification_data = {
            'reference': 'test-ref-123',
            'status': 'completed'
        }
        
        found_tx = self.env['payment.transaction']._get_tx_from_notification_data('odoxpay', notification_data)
        self.assertEqual(found_tx, tx)

    def test_get_tx_from_notification_data_with_session_id(self):
        """ Test finding transaction by session ID. """
        tx = self._create_transaction('redirect', reference='test-ref-456')
        tx.provider_reference = 'session_123'
        
        notification_data = {
            'session_id': 'session_123',
            'status': 'completed'
        }
        
        found_tx = self.env['payment.transaction']._get_tx_from_notification_data('odoxpay', notification_data)
        self.assertEqual(found_tx, tx)

    def test_process_notification_data_completed(self):
        """ Test processing notification data for completed payment. """
        tx = self._create_transaction('redirect')
        
        notification_data = {
            'reference': tx.reference,
            'status': 'completed',
            'session_id': 'session_123'
        }
        
        tx._process_notification_data(notification_data)
        
        self.assertEqual(tx.state, 'done')
        self.assertEqual(tx.provider_reference, 'session_123')

    def test_process_notification_data_pending(self):
        """ Test processing notification data for pending payment. """
        tx = self._create_transaction('redirect')
        
        notification_data = {
            'reference': tx.reference,
            'status': 'pending'
        }
        
        tx._process_notification_data(notification_data)
        
        self.assertEqual(tx.state, 'pending')

    def test_process_notification_data_cancelled(self):
        """ Test processing notification data for cancelled payment. """
        tx = self._create_transaction('redirect')
        
        notification_data = {
            'reference': tx.reference,
            'status': 'cancelled'
        }
        
        tx._process_notification_data(notification_data)
        
        self.assertEqual(tx.state, 'cancel')

    def test_process_notification_data_error(self):
        """ Test processing notification data for error payment. """
        tx = self._create_transaction('redirect')
        
        notification_data = {
            'reference': tx.reference,
            'status': 'rejected',
            'error_code': 'insufficient_funds'
        }
        
        tx._process_notification_data(notification_data)
        
        self.assertEqual(tx.state, 'error')
        self.assertIn('Insufficient funds', tx.state_message)
