# Part of Odoo. See LICENSE file for full copyright and licensing details.

from unittest.mock import patch

from odoo.tests import tagged
from odoo.tools import mute_logger

from odoo.addons.payment.tests.common import PaymentCommon


@tagged('post_install', '-at_install')
class TestPaymentProvider(PaymentCommon):

    def setUp(self):
        super().setUp()
        self.provider = self._prepare_provider('odoxpay', update_values={
            'odoxpay_api_key': 'pk_test_dummy_key',
            'odoxpay_secret_key': 'sk_test_dummy_secret',
            'odoxpay_webhook_secret': 'whsec_dummy_webhook_secret',
        })

    def test_supported_currencies(self):
        """ Test that the provider returns the correct supported currencies. """
        supported_currencies = self.provider._get_supported_currencies()
        self.assertTrue(
            all(currency.name in ['USD', 'EUR', 'BRL', 'ARS', 'MXN', 'COP', 'CLP', 'PEN'] 
                for currency in supported_currencies)
        )

    def test_get_default_payment_method_codes(self):
        """ Test that the provider returns the correct default payment method codes. """
        default_codes = self.provider._get_default_payment_method_codes()
        expected_codes = {'card', 'visa', 'mastercard', 'amex', 'discover'}
        self.assertEqual(default_codes, expected_codes)

    @mute_logger('odoo.addons.payment_odoxpay.models.payment_provider')
    def test_make_request_success(self):
        """ Test successful API request. """
        with patch('requests.request') as mock_request:
            mock_response = mock_request.return_value
            mock_response.raise_for_status.return_value = None
            mock_response.json.return_value = {'status': 'success', 'session_id': 'test_session_123'}
            
            result = self.provider._odoxpay_make_request('/test-endpoint', {'test': 'data'})
            
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['session_id'], 'test_session_123')
            mock_request.assert_called_once()
