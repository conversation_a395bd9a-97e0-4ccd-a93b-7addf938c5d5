# Payment Provider: OdoxPay

This module integrates OdoxPay as a payment provider in Odoo.

## Features

- Support for multiple currencies (USD, EUR, BRL, ARS, MXN, COP, CLP, PEN)
- Credit and debit card payments
- Secure webhook notifications
- Sandbox and production environments
- Refund support
- Manual capture support

## Configuration

1. Install the module
2. Go to Accounting > Configuration > Payment Providers
3. Create or edit the OdoxPay provider
4. Configure your API credentials:
   - API Key
   - Secret Key
   - Webhook Secret (optional, for signature verification)

## API Endpoints

- Production: `https://api.odoxpay.com`
- Sandbox: `https://sandbox-api.odoxpay.com`

## Supported Payment Methods

- Visa
- Mastercard
- American Express
- Discover

## Webhook URLs

The module automatically configures the following webhook URLs:
- Return URL: `/payment/odoxpay/return`
- Cancel URL: `/payment/odoxpay/cancel`
- Webhook URL: `/payment/odoxpay/webhook`

## Testing

Use the sandbox environment for testing. Make sure to use test API keys provided by OdoxPay.

## Support

For technical support, please contact the module maintainer or refer to the OdoxPay API documentation.
