<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="payment_provider_odoxpay" model="payment.provider">
            <field name="name">OdoxPay</field>
            <field name="code">odoxpay</field>
            <field name="sequence">10</field>
            <field name="image_128" type="base64" file="payment_odoxpay/static/description/icon.png"/>
            <field name="state">disabled</field>
            <field name="is_published">false</field>
            <field name="payment_icon_ids" eval="[(6, 0, [
                ref('payment.payment_icon_cc_visa'),
                ref('payment.payment_icon_cc_mastercard'),
                ref('payment.payment_icon_cc_amex'),
                ref('payment.payment_icon_cc_discover')
            ])]"/>
            <field name="support_manual_capture">manual</field>
            <field name="support_refund">full_only</field>
            <field name="support_tokenization">false</field>
            <field name="redirect_form_view_id" ref="redirect_form"/>
            <field name="inline_form_view_id" ref="payment_form"/>
        </record>

    </data>
</odoo>
