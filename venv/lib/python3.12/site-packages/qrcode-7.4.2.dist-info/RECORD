../../../bin/qr,sha256=TEI0aQ0gZF-RIN7qB3vCNh3iVX5_O_QUtbOI8AubpWI,299
../../../share/man/man1/qr.1,sha256=1HjEKPDD7Y2hvYbwetA4Qs2ZOrF4kRKO6XUBnNL3YDE,1355
qrcode-7.4.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
qrcode-7.4.2.dist-info/LICENSE,sha256=QN-5A8lO4_eJUAExMRGGVI7Lpc79NVdiPXcA4lIquZQ,2143
qrcode-7.4.2.dist-info/METADATA,sha256=JsgthBQOLX0gB21FIZnpr2LwIMfE8KkwegPy19a_u8A,17096
qrcode-7.4.2.dist-info/RECORD,,
qrcode-7.4.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
qrcode-7.4.2.dist-info/WHEEL,sha256=2wepM1nk4DS4eFpYrW1TTqPcoGNfHhhO_i5m4cOimbo,92
qrcode-7.4.2.dist-info/entry_points.txt,sha256=McEjM7MauRTGEVu8d0Ajy4F2Bex6qEawTYQsctfVbjk,51
qrcode-7.4.2.dist-info/top_level.txt,sha256=lJ7l1nyDt4uWLvG9GVo-Zo-rlkBsRRkXe45ps_jmq3c,7
qrcode/LUT.py,sha256=NjXKPfHSTFYoLlGkXhFjf2OUq_EGD6mrdyYHIG3dNck,3599
qrcode/__init__.py,sha256=0C8jx3gDHSJ4yydlHN01ytyipNh2pMO3VYS9Dk-m4oU,645
qrcode/__pycache__/LUT.cpython-312.pyc,,
qrcode/__pycache__/__init__.cpython-312.pyc,,
qrcode/__pycache__/base.cpython-312.pyc,,
qrcode/__pycache__/console_scripts.cpython-312.pyc,,
qrcode/__pycache__/constants.cpython-312.pyc,,
qrcode/__pycache__/exceptions.cpython-312.pyc,,
qrcode/__pycache__/main.cpython-312.pyc,,
qrcode/__pycache__/release.cpython-312.pyc,,
qrcode/__pycache__/util.cpython-312.pyc,,
qrcode/base.py,sha256=9J_1LynF5dXJK14Azs8XyHJY66FfTluYJ66F8ZjeStY,7288
qrcode/compat/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
qrcode/compat/__pycache__/__init__.cpython-312.pyc,,
qrcode/compat/__pycache__/etree.cpython-312.pyc,,
qrcode/compat/__pycache__/pil.cpython-312.pyc,,
qrcode/compat/etree.py,sha256=rEyWRA9QMsVFva_9rOdth3RAkRpFOmkF59c2EQM44gE,152
qrcode/compat/pil.py,sha256=9fbuYrvq7AG4TURpBdi_dZ3_L_vqpFO7Qc0280vLIIY,362
qrcode/console_scripts.py,sha256=W5ji79UtPxgVqngwztA3R17HylH_0D7Ve6y_WN5kZRA,5571
qrcode/constants.py,sha256=0Csa8YYdeQ8NaFrRmt43maVg12O89d-oKgiKAVIO2s4,106
qrcode/exceptions.py,sha256=L2fZuYOKscvdn72ra-wF8Gwsr2ZB9eRZWrp1f0IDx4E,45
qrcode/image/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
qrcode/image/__pycache__/__init__.cpython-312.pyc,,
qrcode/image/__pycache__/base.cpython-312.pyc,,
qrcode/image/__pycache__/pil.cpython-312.pyc,,
qrcode/image/__pycache__/pure.cpython-312.pyc,,
qrcode/image/__pycache__/styledpil.cpython-312.pyc,,
qrcode/image/__pycache__/svg.cpython-312.pyc,,
qrcode/image/base.py,sha256=1xMhPfb8317m2Ysbl2p2rVtBwcx6bqmQEVDzkR55M9M,4984
qrcode/image/pil.py,sha256=YahBtPLT_7EUFSEP3poWTtAlEZIB7zNU5JNtlWLQjYU,1524
qrcode/image/pure.py,sha256=lpMH2i0hTzVsywEFkjifC_dBiICN3rjDEeeSKBePcBA,1412
qrcode/image/styledpil.py,sha256=2qYcdZaPp0_x0byj2FDcZSzgZiQyKlJKfEwkiOXLB-w,4477
qrcode/image/styles/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
qrcode/image/styles/__pycache__/__init__.cpython-312.pyc,,
qrcode/image/styles/__pycache__/colormasks.cpython-312.pyc,,
qrcode/image/styles/colormasks.py,sha256=jb-uydg25fkKhiAO8xoTaAP8Q-Vz3Y9nnSliUKD0oik,7601
qrcode/image/styles/moduledrawers/__init__.py,sha256=Mklw5SjYiGbs2Aym38jwwrKt0plJGzwIVgZ--jiOVBc,430
qrcode/image/styles/moduledrawers/__pycache__/__init__.cpython-312.pyc,,
qrcode/image/styles/moduledrawers/__pycache__/base.cpython-312.pyc,,
qrcode/image/styles/moduledrawers/__pycache__/pil.cpython-312.pyc,,
qrcode/image/styles/moduledrawers/__pycache__/svg.cpython-312.pyc,,
qrcode/image/styles/moduledrawers/base.py,sha256=WL3uefhVeLIKyCVpJpKTkv_ihS9SJt-DCnnQwizasac,1067
qrcode/image/styles/moduledrawers/pil.py,sha256=myZ-dmDiEhE7_KB9t1bbHFeDHs4-B6Vu8diY1xjX4zQ,9852
qrcode/image/styles/moduledrawers/svg.py,sha256=_ZOb60IVmyT3_q6uORwgJo3H8-MgSdDsJ1Q01ZzMi4U,3952
qrcode/image/svg.py,sha256=dNWQfIQ-_t07d4dPYWfd0ghg4KL9K8f0ho7zWn95SlQ,5246
qrcode/main.py,sha256=jzPEBFIpr9EHV-v7jOh3NW1y53GJ8bbYyV7HhHK4L3w,16462
qrcode/release.py,sha256=p5oZkhKDcc9fYxXpadhmByLYYhGFUoTyh_f1KzBLW5U,1079
qrcode/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
qrcode/tests/__pycache__/__init__.cpython-312.pyc,,
qrcode/tests/__pycache__/test_example.cpython-312.pyc,,
qrcode/tests/__pycache__/test_qrcode.cpython-312.pyc,,
qrcode/tests/__pycache__/test_qrcode_svg.cpython-312.pyc,,
qrcode/tests/__pycache__/test_release.cpython-312.pyc,,
qrcode/tests/__pycache__/test_script.cpython-312.pyc,,
qrcode/tests/__pycache__/test_util.cpython-312.pyc,,
qrcode/tests/test_example.py,sha256=Z3rYh8MZaJT08YgJNEN1pEq5ezqNP9akpKnNlQY4His,333
qrcode/tests/test_qrcode.py,sha256=SZZ461_IrIrEG5x30gRYlk2dTsSe-cxaaGKabVuKX_E,17398
qrcode/tests/test_qrcode_svg.py,sha256=42-BoSUdIwU2XfxiZSS5jXDHrkscn9Cm_bTvLSPbCWE,1644
qrcode/tests/test_release.py,sha256=lthfxR_oaSrMpx5rwD474i3_5aYuXW5vrtK8x0pYXSo,1468
qrcode/tests/test_script.py,sha256=597ta1l-dFNuRp8FUhQ7ZovidxgG5uUNbMsmKXEBF-E,3807
qrcode/tests/test_util.py,sha256=ZMapsEzOYMthbIx7DwZZGGPW2Vr-X-gLKWH2KArTnXo,277
qrcode/util.py,sha256=dMBLr8VCsyY5sDlJg0MpggyO9wU-BodoIExQHiVaDlY,17128
