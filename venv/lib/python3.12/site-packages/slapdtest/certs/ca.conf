# Written by <PERSON>

[default]
ca = "ca"
tmpdir = $ENV::CATMPDIR
outdir = $ENV::CAOUTDIR
name_opt = multiline,-esc_msb,utf8

[req]
default_bits = 2048
encrypt_key = no
default_md = sha256
utf8 = yes
string_mask = utf8only
prompt = no
distinguished_name = ca_dn

[ca_dn]
countryName = "DE"
organizationName = "python-ldap"
organizationalUnitName = "slapd-test"
commonName = "Python LDAP Test CA"

[ca]
default_ca = python_ldap_ca

[python_ldap_ca]
certificate = $outdir/$ca.pem
private_key = $outdir/$ca.key
new_certs_dir = $tmpdir
serial = $tmpdir/$ca.crt.srl
crlnumber = $tmpdir/$ca.crl.srl
database = $tmpdir/$ca.db
unique_subject = no
default_days = 365200
default_md = sha256
policy = match_pol
email_in_dn = no
preserve = no
name_opt = $name_opt
cert_opt = ca_default
copy_extensions = none
default_crl_days = 365100

[match_pol]
countryName = match
stateOrProvinceName = optional
localityName = optional
organizationName = match
organizationalUnitName = match
commonName = supplied

[ca_ext]
basicConstraints = critical,CA:true
keyUsage = critical,keyCertSign,cRLSign
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always

[server_san]
DNS.1 = localhost
IP.1 = 127.0.0.1
IP.2 = ::1

[server_ext]
basicConstraints = critical,CA:false
keyUsage = critical,digitalSignature,keyEncipherment
extendedKeyUsage = critical,serverAuth
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always
subjectAltName = @server_san

[client_ext]
basicConstraints = critical,CA:false
keyUsage = critical,digitalSignature
extendedKeyUsage = critical,clientAuth
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always
