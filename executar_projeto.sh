#!/bin/bash

set -e

DIRETORIO_PROJETO="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ARQUIVO_CONFIGURACAO="$DIRETORIO_PROJETO/odoo.conf"
ARQUIVO_REQUISITOS="$DIRETORIO_PROJETO/requirements.txt"
EXECUTAVEL_ODOO="$DIRETORIO_PROJETO/odoo-bin"

verificar_python() {
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3 não encontrado. Instale o Python 3.10 ou superior."
        exit 1
    fi
    
    versao_python=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    echo "✅ Python $versao_python encontrado"
    
    if [[ $(echo "$versao_python >= 3.10" | bc) -eq 0 ]]; then
        echo "❌ Python 3.10 ou superior é necessário. Versão atual: $versao_python"
        exit 1
    fi
}

verificar_postgresql() {
    if ! command -v psql &> /dev/null; then
        echo "⚠️  PostgreSQL não encontrado. Instale o PostgreSQL:"
        echo "   brew install postgresql"
        echo "   brew services start postgresql"
        return 1
    fi
    
    if ! brew services list | grep postgresql | grep started &> /dev/null; then
        echo "🔄 Iniciando PostgreSQL..."
        brew services start postgresql
    fi
    
    echo "✅ PostgreSQL está rodando"
    return 0
}

criar_banco_dados() {
    local nome_banco="odoo_db"
    local usuario_banco="odoo"
    local senha_banco="odoo"
    
    if ! psql -lqt | cut -d \| -f 1 | grep -qw "$nome_banco"; then
        echo "🔄 Criando banco de dados..."
        
        if ! psql -c "SELECT 1 FROM pg_roles WHERE rolname='$usuario_banco'" | grep -q 1; then
            createuser -s "$usuario_banco" 2>/dev/null || true
            psql -c "ALTER USER $usuario_banco WITH PASSWORD '$senha_banco';" 2>/dev/null || true
        fi
        
        createdb -O "$usuario_banco" "$nome_banco" 2>/dev/null || true
        echo "✅ Banco de dados criado"
    else
        echo "✅ Banco de dados já existe"
    fi
}

instalar_dependencias() {
    echo "🔄 Verificando dependências..."
    
    if [ -d ".venv" ]; then
        source .venv/bin/activate
    elif [ -d "venv" ]; then
        source venv/bin/activate
    else
        echo "🔄 Criando ambiente virtual..."
        python3 -m venv venv
        source venv/bin/activate
    fi
    
    echo "🔄 Instalando dependências..."
    pip install --upgrade pip
    pip install -r "$ARQUIVO_REQUISITOS"
    
    echo "✅ Dependências instaladas"
}

executar_odoo() {
    echo "🚀 Iniciando Odoo..."
    
    if [ -n "$VIRTUAL_ENV" ]; then
        echo "✅ Usando ambiente virtual ativo: $VIRTUAL_ENV"
    elif [ -d ".venv" ]; then
        source .venv/bin/activate
    elif [ -d "venv" ]; then
        source venv/bin/activate
    else
        echo "❌ Nenhum ambiente virtual encontrado. Execute: $0 setup"
        exit 1
    fi
    
    local argumentos_execucao=(
        "--config=$ARQUIVO_CONFIGURACAO"
        "--addons-path=./addons,./odoo/addons"
        "--dev=reload,qweb,werkzeug,xml"
    )
    
    if [ "$1" = "init" ]; then
        argumentos_execucao+=("--init=base" "--stop-after-init")
        echo "🔄 Inicializando banco de dados..."
    elif [ "$1" = "update" ]; then
        argumentos_execucao+=("--update=all" "--stop-after-init")
        echo "🔄 Atualizando módulos..."
    fi
    
    python3 "$EXECUTAVEL_ODOO" "${argumentos_execucao[@]}"
}

mostrar_ajuda() {
    echo "📋 Uso: $0 [comando]"
    echo ""
    echo "Comandos:"
    echo "  start     - Inicia o servidor Odoo"
    echo "  init      - Inicializa o banco de dados"
    echo "  update    - Atualiza todos os módulos"
    echo "  setup     - Configura o ambiente completo"
    echo "  help      - Mostra esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  $0 setup   # Primeira execução"
    echo "  $0 start   # Executar normalmente"
}

executar_setup_completo() {
    echo "🔧 Configurando ambiente Odoo..."
    verificar_python
    verificar_postgresql || {
        echo "⚠️  PostgreSQL não está disponível. Configure manualmente."
        echo "   Instale: brew install postgresql"
        echo "   Inicie: brew services start postgresql"
        exit 1
    }
    criar_banco_dados
    instalar_dependencias
    executar_odoo init
    echo ""
    echo "✅ Setup concluído! Execute '$0 start' para iniciar o servidor."
    echo "🌐 Acesse: http://localhost:8069"
}

main() {
    cd "$DIRETORIO_PROJETO"
    
    case "${1:-start}" in
        "setup")
            executar_setup_completo
            ;;
        "start")
            verificar_python
            if [ -z "$VIRTUAL_ENV" ] && [ ! -d "venv" ] && [ ! -d ".venv" ]; then
                echo "❌ Ambiente não configurado. Execute: $0 setup"
                exit 1
            fi
            executar_odoo
            ;;
        "init")
            verificar_python
            executar_odoo init
            ;;
        "update")
            verificar_python
            executar_odoo update
            ;;
        "help"|"-h"|"--help")
            mostrar_ajuda
            ;;
        *)
            echo "❌ Comando inválido: $1"
            mostrar_ajuda
            exit 1
            ;;
    esac
}

main "$@"
